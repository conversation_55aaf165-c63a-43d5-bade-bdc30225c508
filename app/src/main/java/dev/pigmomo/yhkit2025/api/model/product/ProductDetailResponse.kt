package dev.pigmomo.yhkit2025.api.model.product

/**
 * 商品详情响应数据模型
 */
data class ProductDetailResponse(
    val code: Int,
    val message: String,
    val data: ProductDetailData?
)

/**
 * 商品详情数据
 */
data class ProductDetailData(
    val sku: SkuDetail?,
    val spu: SpuDetail?,
    val shop: ShopInfo?,
    val delivery: DeliveryInfo?,
    val promotion: PromotionInfo?,
    val stock: StockInfo?,
    val recommend: RecommendInfo?
)

/**
 * SKU详情
 */
data class SkuDetail(
    val skuCode: String?,
    val skuId: String?,
    val title: String?,
    val subTitle: String?,
    val description: String?,
    val images: List<ProductImage>?,
    val price: ProductPrice?,
    val unit: String?,
    val weight: String?,
    val brand: BrandInfo?,
    val category: CategoryDetail?,
    val attributes: List<ProductAttribute>?,
    val tags: List<ProductTag>?,
    val isOnSale: Boolean?,
    val inStock: Int?,
    val skuType: Int?,
    val skuSaleType: Int?
)

/**
 * SPU详情
 */
data class SpuDetail(
    val spuId: String?,
    val spuCode: String?,
    val title: String?,
    val description: String?,
    val specifications: List<Specification>?
)

/**
 * 商品图片
 */
data class ProductImage(
    val url: String?,
    val type: String?,
    val sort: Int?
)

/**
 * 商品价格
 */
data class ProductPrice(
    val price: String?,
    val originalPrice: String?,
    val marketPrice: String?,
    val memberPrice: String?,
    val priceInCent: Int?,
    val originalPriceInCent: Int?,
    val marketPriceInCent: Int?,
    val memberPriceInCent: Int?,
    val discount: String?,
    val discountRate: String?
)

/**
 * 品牌信息
 */
data class BrandInfo(
    val brandId: String?,
    val brandName: String?,
    val brandLogo: String?
)

/**
 * 分类详情
 */
data class CategoryDetail(
    val categoryId: String?,
    val categoryName: String?,
    val parentCategoryId: String?,
    val parentCategoryName: String?,
    val level: Int?
)

/**
 * 商品属性
 */
data class ProductAttribute(
    val name: String?,
    val value: String?,
    val type: String?
)

/**
 * 商品标签
 */
data class ProductTag(
    val text: String?,
    val type: String?,
    val color: String?,
    val backgroundColor: String?
)

/**
 * 店铺信息
 */
data class ShopInfo(
    val shopId: String?,
    val shopName: String?,
    val sellerId: String?,
    val sellerName: String?,
    val address: String?,
    val phone: String?,
    val businessHours: String?
)

/**
 * 配送信息
 */
data class DeliveryInfo(
    val deliveryType: List<String>?,
    val deliveryFee: String?,
    val freeDeliveryAmount: String?,
    val estimatedTime: String?,
    val serviceArea: String?
)

/**
 * 促销信息
 */
data class PromotionInfo(
    val promotions: List<Promotion>?,
    val coupons: List<CouponInfo>?,
    val activities: List<ActivityInfo>?
)

/**
 * 促销活动
 */
data class Promotion(
    val id: String?,
    val title: String?,
    val description: String?,
    val type: String?,
    val startTime: String?,
    val endTime: String?,
    val discount: String?
)

/**
 * 优惠券信息
 */
data class CouponInfo(
    val couponId: String?,
    val title: String?,
    val description: String?,
    val amount: String?,
    val threshold: String?,
    val validTime: String?
)

/**
 * 活动信息
 */
data class ActivityInfo(
    val activityId: String?,
    val title: String?,
    val description: String?,
    val type: String?,
    val startTime: String?,
    val endTime: String?
)

/**
 * 库存信息
 */
data class StockInfo(
    val stockQuantity: Int?,
    val stockStatus: String?,
    val maxPurchaseQuantity: Int?,
    val minPurchaseQuantity: Int?
)

/**
 * 推荐信息
 */
data class RecommendInfo(
    val relatedProducts: List<RelatedProduct>?,
    val similarProducts: List<SimilarProduct>?
)

/**
 * 相关商品
 */
data class RelatedProduct(
    val skuCode: String?,
    val title: String?,
    val image: String?,
    val price: String?,
    val originalPrice: String?
)

/**
 * 相似商品
 */
data class SimilarProduct(
    val skuCode: String?,
    val title: String?,
    val image: String?,
    val price: String?,
    val originalPrice: String?,
    val similarity: Double?
)

/**
 * 规格信息
 */
data class Specification(
    val name: String?,
    val value: String?,
    val unit: String?
)
