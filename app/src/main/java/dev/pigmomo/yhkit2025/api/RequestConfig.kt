package dev.pigmomo.yhkit2025.api

import java.util.concurrent.TimeUnit

/**
 * API配置类
 * 存储API相关的配置信息，如基础URL、超时时间等
 */
object RequestConfig {
    /**
     * API基础URL
     */
    var APP_BASE_URL = "https://api.yonghuivip.com"

    var MINI_PROGRAM_BASE_URL = "https://activity.yonghuivip.com"

    /**
     * 业务参数原始值
     */
    const val XYH_BIZ_PARAMS_ORIGIN_APP = "gvo=&gib=&xdotdy=&vkkdy=dpnrxsquou_*a_++ix"

    const val XYH_BIZ_PARAMS_ORIGIN_MINI_PROGRAM = "gvo=gib=&vyymznnMy=&vkkdy=rsx,xa(x,*!,,zz'\$!&xdotdy=&ncjkdy=&nzggzmdy=&vmzv="
    
    /**
     * 超时配置（秒）
     */
    object Timeout {
        const val CONNECT = 30L
        const val READ = 30L
        const val WRITE = 30L

        // 助力券专用超时配置（更短的超时时间）
        const val BOOST_COUPON_CONNECT = 15L
        const val BOOST_COUPON_READ = 30L
        const val BOOST_COUPON_WRITE = 15L

        /**
         * 获取OkHttp超时配置（毫秒）
         */
        fun getConnectTimeoutMillis() = TimeUnit.SECONDS.toMillis(CONNECT)
        fun getReadTimeoutMillis() = TimeUnit.SECONDS.toMillis(READ)
        fun getWriteTimeoutMillis() = TimeUnit.SECONDS.toMillis(WRITE)

        /**
         * 获取助力券专用超时配置（毫秒）
         */
        fun getBoostCouponConnectTimeoutMillis() = TimeUnit.SECONDS.toMillis(BOOST_COUPON_CONNECT)
        fun getBoostCouponReadTimeoutMillis() = TimeUnit.SECONDS.toMillis(BOOST_COUPON_READ)
        fun getBoostCouponWriteTimeoutMillis() = TimeUnit.SECONDS.toMillis(BOOST_COUPON_WRITE)
    }
    
    /**
     * 是否启用调试日志
     */
    var DEBUG = false

    /**
     * 应用版本信息
     */
    object AppVersion {
        const val VERSION = "********"
        const val VERSION_NUMBER = "**********"
    }

    /**
     * 小程序版本信息
     */
    object MiniProgramVersion {
        const val VERSION = "**********"
        const val SDK_VERSION = "3.8.12"
        const val WECHAT_VERSION = "8.0.61"
        const val OPEN_ID = "oP_Ic0Zm3vu6JbtcY-_wevQ3pzKQ"
    }

    /**
     * 检查账户类型
     */
    object CheckAccountType {
        const val CITYID = "4"
        const val SELLERID = "7"
        const val SHOPID = "9M7W"
    }
    
    /**
     * API路径
     */
    object Path {
        // 用户信息
        const val USER_INFO_PATH = "/web/user/member/getinfo/760"

        //地址
        const val ADDRESS_LIST_PATH = "/web/user/address/list/700"
        const val ADD_ADDRESS_PATH = "/web/user/address/save/700"
        const val DELETE_ADDRESS_PATH = "/web/user/address/delete/775"

        // 首页横幅
        const val FP_HOMEPAGE_PATH = "/api/fp/homepage" //检查新人特权，判断账户类型
        const val FP_HOMEPAGE_PATH_MINI = "/api/app/fp/homepage" //检查新人特权，判断账户类型

        // 积分明细
        const val CREDIT_DETAILS_PATH = "/web/coupon/credit/details"

        // 购物车相关
        const val CART_LIST_PATH = "/web/trade/cart/list/7125"
        const val CART_CHANGE_PATH = "/web/trade/cart/change/700"
        const val CART_CLEAR_PATH = "/web/trade/cart/delete/700"
        const val ADD_CART_PATH = "/web/trade/cart/cartlite/700"
        
        // 店铺相关
        const val FB_SHOP_LBS_PATH = "/api/fp/shoplbs/home" //获取当前地址所属的店铺
        const val FB_SHOP_LBS_PATH_MINI = "/api/app/fp/shoplbs/wechat/miniprogram/775" //获取当前地址所属的店铺
        
        // 订单相关
        const val ORDER_PLACE_PATH = "/web/trade/order/place/785"
        const val ORDER_CONFIRM_PATH = "/web/trade/order/confirm/750"
        const val ORDER_LIST_PATH = "/web/trade/order/orderlist/750"
        const val ORDER_DETAIL_PATH = "/web/trade/order/orderdetail/750"
        const val ORDER_DELETE_PATH = "/web/trade/order/delete/750"
        const val ORDER_CANCEL_PATH = "/web/trade/order/cancel/750"
        const val ORDER_PRE_PAY_PATH = "/web/pay/payment/prePay/770"
        const val ORDER_RED_ENVELOPE_PATH = "/web/trade/order/redenvelope/check/885"
        const val ORDER_RED_ENVELOPE_PATH_MINI = "/api/web/trade/order/redenvelope/check/885"
        const val CAN_UPDATE_DELIVERY_INFO_PATH = "/web/trade/order/canupdatedeliveryinfo/830"
        const val GET_ORDER_DELIVERY_INFO_PATH = "/web/trade/order/getOrderDeliveryInfo/815"
        const val UPDATE_DELIVERY_INFO_PATH = "/web/trade/order/updatedeliveryinfo/815"
        const val AFTER_SALES_PATH = "/web/trade/aftersales/refund/create/775"
        const val INVOICE_CAN_APPLY_ORDER_LIST_PATH = "/web/trade/order/invoicecanapplyorderlist/750"
        const val AFTER_SALES_LIST_PATH = "/web/trade/aftersales/list/775"
        
        // 永辉卡
        const val CARD_LIST_PATH = "/web/card/cardList"
        const val CARD_TRANS_INFO_LIST_BY_PAGE_PATH = "/web/card/cardTransInfoListByPage" //获取永辉卡记录的订单
        const val CARD_SIMPLE_PRESENT_PATH = "/web/card/simple-present"
        const val CANCEL_SEND_CARD_PATH = "/web/card/cancelSendCard"
        const val SEND_CARD_PATH = "/web/card/sendCard"
        const val VALIDATE_CARD_PATH = "/web/card/validateCardOrLink"
        const val BIND_CARD_PATH = "/web/card/bindCardOrLink"
        const val RECEIVE_CARD_PATH = "/web/card/receiveCard"
        const val PLACE_CARD_PATH = "/web/card/place" //检查是否能够购买永辉卡
        const val CARD_ALL_BALANCE_PATH = "/web/card/cardIndexInfo"
        
        // 新人福利
        const val NEW_PERSON_POPUP_PATH = "/web/marketing/newPerson/newPersonPopup"
        const val NEW_USER_COUPON_INFO_PATH = "/web/groupon/cmactivity/newusercoupon/info"
        const val NEW_USER_COUPON_RECEIVE_PATH = "/web/groupon/cmactivity/newusercoupon/receive"
        const val NEW_PERSON_COUPON_INFO_PATH = "/web/coupon/newPerson/queryNewPersonAssemblyData"

        // 邀请有礼
        const val INVITATION_V2_ACTIVITY_INFO_PATH = "/web/invitation/invitationV2/loadActivityInfo"
        const val INVITATION_V2_BIND_PATH = "/web/coupon/invitationV2/bind"
        const val SUCCESS_INVITE_LIST_PATH = "/web/invitation/invitationV2/getsuccessinvitelist"
        const val TOTAL_REWARD_LIST_PATH = "/web/invitation/invitationV2/gettotalrewardlist"
        
        // 优惠券
        const val COUPON_LIST_PATH = "/web/user/coupon/v4/mine/current/760"
        const val KIND_COUPON_PATH = "/web/user/coupon/kind/760"
        const val CMS_ACTIVITY_REST_PATH = "/webapi/cms-activity-rest/h5coupon/send/single"
        const val MONEY_SAVING_CARD_FREE_CARD_PATH = "/web/economicalcard/moneysavingcard/freeCard"

        // 拼手气
        const val HAND_LUCK_GET_SLOT_MACHINE_DATA_PATH = "/web/coupon/handluck/getSlotMachineData"
        const val HAND_LUCK_DRAW_LOTTERY_PATH = "/web/coupon/handluck/drawLottery"
        const val HAND_LUCK_GET_TOTAL_REWARD_LIST_PATH = "/web/coupon/handluck/rewardList"

        // 积分组队
        const val SIGN_REWARD_DETAIL_PATH = "/web/coupon/signreward/detail"
        const val TEAM_DETAIL_PATH = "/web/coupon/credit/dividePoint/teamDetail"
        const val JOIN_THE_PARTY_PATH = "/web/coupon/credit/dividePoint/joinTheParty"

        // 助力券
        const val BOOST_COUPON_LIST_PATH = "/web/marketing/boostcoupon/listBoostCouponByPage"
        const val GET_GAME_CODE_PATH = "/web/marketing/boostcoupon/getGameCode"
        const val BOOST_COUPON_PATH = "/web/marketing/boostcoupon/boost"

        // 搜索相关
        const val KEYWORD_SEARCH_PATH = "/web/search/keywordSearch/v700"

        // 商品相关
        const val SKU_DETAIL_PATH = "/web/product/sku/detail/780"
    }

    /**
     * APP基础业务参数
     */
    object AppBaseBusinessParams {
        const val OS = "android"
        const val ELDERLY = "0"
        const val PLATFORM = "Android"
        const val CHANNEL_MAIN = "official"
        const val PRODUCT_LINE = "YhStore"
        const val CHANNEL_SUB = ""
    }

    /**
     * 小程序基础业务参数
     */
    object MiniProgramBaseBusinessParams {
        const val OS = "android"
        const val PLATFORM = "wechatminiprogram"
        const val CHANNEL = "512"
        const val CHANNEL_MAIN = "official"
        const val PRODUCT_LINE = "YhStore"
        const val APP_TYPE = "miniprogram"
        const val PROPORTION = "3.5"
        const val APP_ID = "wxc9cf7c95499ee604"
        const val CHANNEL_SUB = ""
    }
    
    /**
     * 获取完整的API URL
     * @param path API路径
     * @return 完整的API URL
     */
    fun getAppFullUrl(path: String): String {
        return APP_BASE_URL + path
    }

    fun getMiniProgramFullUrl(path: String): String {
        return MINI_PROGRAM_BASE_URL + path
    }
} 