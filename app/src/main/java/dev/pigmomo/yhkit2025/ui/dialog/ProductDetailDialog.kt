package dev.pigmomo.yhkit2025.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import coil.compose.AsyncImage
import coil.request.ImageRequest
import dev.pigmomo.yhkit2025.api.model.product.ProductDetailData
import dev.pigmomo.yhkit2025.ui.theme.DialogContainerColor
import dev.pigmomo.yhkit2025.ui.theme.RoyalBlue

/**
 * 商品详情弹窗
 * 
 * @param productData 商品详情数据
 * @param onDismiss 关闭弹窗回调
 * @param onAddToCart 添加到购物车回调
 * @param onFavoriteToggle 收藏切换回调
 * @param onImageClick 图片点击回调（用于查看大图）
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductDetailDialog(
    productData: ProductDetailData,
    onDismiss: () -> Unit,
    onAddToCart: ((String, Int) -> Unit)? = null,
    onFavoriteToggle: ((String, Boolean) -> Unit)? = null,
    onImageClick: ((String) -> Unit)? = null
) {
    var selectedImageIndex by remember { mutableIntStateOf(0) }
    var quantity by remember { mutableIntStateOf(1) }
    var isFavorite by remember { mutableStateOf(productData.favoriteButton?.state == 1) }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.95f)
                .fillMaxHeight(0.9f)
                .padding(8.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = DialogContainerColor)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // 标题栏
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "商品详情",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = RoyalBlue
                    )
                    
                    Row {
                        // 收藏按钮
                        IconButton(
                            onClick = {
                                isFavorite = !isFavorite
                                onFavoriteToggle?.invoke(productData.id ?: "", isFavorite)
                            }
                        ) {
                            Icon(
                                imageVector = if (isFavorite) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                                contentDescription = "收藏",
                                tint = if (isFavorite) Color.Red else Color.Gray
                            )
                        }
                        
                        // 关闭按钮
                        IconButton(onClick = onDismiss) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "关闭",
                                tint = Color.Gray
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 内容区域
                LazyColumn(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 商品图片轮播
                    item {
                        ProductImageCarousel(
                            images = productData.mainimgs ?: emptyList(),
                            selectedIndex = selectedImageIndex,
                            onImageSelected = { selectedImageIndex = it },
                            onImageClick = onImageClick
                        )
                    }

                    // 商品基本信息
                    item {
                        ProductBasicInfo(productData)
                    }

                    // 价格信息
                    item {
                        ProductPriceInfo(productData)
                    }

                    // 库存信息
                    item {
                        ProductStockInfo(productData)
                    }

                    // 商品属性
                    item {
                        ProductAttributes(productData)
                    }

                    // 评论信息
                    item {
                        ProductCommentInfo(productData)
                    }

                    // 促销信息
                    item {
                        ProductPromotionInfo(productData)
                    }

                    // 卖家信息
                    item {
                        ProductSellerInfo(productData)
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // 底部操作栏
                ProductActionBar(
                    productData = productData,
                    quantity = quantity,
                    onQuantityChange = { quantity = it },
                    onAddToCart = { onAddToCart?.invoke(productData.id ?: "", quantity) }
                )
            }
        }
    }
}

/**
 * 商品图片轮播组件
 */
@Composable
private fun ProductImageCarousel(
    images: List<dev.pigmomo.yhkit2025.api.model.product.MainImage>,
    selectedIndex: Int,
    onImageSelected: (Int) -> Unit,
    onImageClick: ((String) -> Unit)?
) {
    Column {
        // 主图片
        if (images.isNotEmpty()) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(images[selectedIndex].imgurl)
                    .crossfade(true)
                    .build(),
                contentDescription = "商品图片",
                modifier = Modifier
                    .fillMaxWidth()
                    .height(250.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .clickable { 
                        onImageClick?.invoke(images[selectedIndex].imgurl ?: "") 
                    },
                contentScale = ContentScale.Crop
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 图片缩略图
        if (images.size > 1) {
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(images.size) { index ->
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(images[index].imgurl)
                            .crossfade(true)
                            .build(),
                        contentDescription = "缩略图",
                        modifier = Modifier
                            .size(60.dp)
                            .clip(RoundedCornerShape(8.dp))
                            .border(
                                width = if (index == selectedIndex) 2.dp else 0.dp,
                                color = if (index == selectedIndex) RoyalBlue else Color.Transparent,
                                shape = RoundedCornerShape(8.dp)
                            )
                            .clickable { onImageSelected(index) },
                        contentScale = ContentScale.Crop
                    )
                }
            }
        }
    }
}

/**
 * 商品基本信息组件
 */
@Composable
private fun ProductBasicInfo(productData: ProductDetailData) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 商品标题
            Text(
                text = productData.title ?: "未知商品",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )

            // 商品副标题
            if (!productData.subtitle.isNullOrEmpty()) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = productData.subtitle,
                    fontSize = 14.sp,
                    color = Color.Gray,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // 商品ID
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = "商品编码: ${productData.id ?: "未知"}",
                fontSize = 12.sp,
                color = Color.Gray
            )
        }
    }
}

/**
 * 商品价格信息组件
 */
@Composable
private fun ProductPriceInfo(productData: ProductDetailData) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = "价格信息",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = RoyalBlue
            )

            Spacer(modifier = Modifier.height(8.dp))

            productData.price?.let { price ->
                Row(
                    verticalAlignment = Alignment.Bottom,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 当前价格
                    Text(
                        text = "¥${String.format("%.2f", (price.value ?: 0) / 100.0)}",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Red
                    )

                    // 市场价
                    if ((price.market ?: 0) > (price.value ?: 0)) {
                        Text(
                            text = "¥${String.format("%.2f", (price.market ?: 0) / 100.0)}",
                            fontSize = 14.sp,
                            color = Color.Gray,
                            textDecoration = TextDecoration.LineThrough
                        )

                        // 折扣
                        val discount = ((price.market!! - price.value!!) * 100.0 / price.market).toInt()
                        Text(
                            text = "${discount}%OFF",
                            fontSize = 12.sp,
                            color = Color.White,
                            modifier = Modifier
                                .background(
                                    Color.Red,
                                    RoundedCornerShape(4.dp)
                                )
                                .padding(horizontal = 4.dp, vertical = 2.dp)
                        )
                    }
                }

                // 规格
                if (!price.spec.isNullOrEmpty()) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "规格: ${price.spec}",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }
            }
        }
    }
}

/**
 * 商品库存信息组件
 */
@Composable
private fun ProductStockInfo(productData: ProductDetailData) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = "库存信息",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = RoyalBlue
            )

            Spacer(modifier = Modifier.height(8.dp))

            productData.stock?.let { stock ->
                Row(
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 库存描述
                    if (!stock.desc.isNullOrEmpty()) {
                        Text(
                            text = stock.desc,
                            fontSize = 14.sp,
                            color = if (stock.count ?: 0 > 0) Color.Green else Color.Red,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    // 库存数量
                    Text(
                        text = "库存: ${stock.count ?: 0}",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }

                // 最小购买数量
                if ((stock.minNum ?: 0) > 0) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "最小购买数量: ${stock.minNum}",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }
            }
        }
    }
}

/**
 * 商品属性组件
 */
@Composable
private fun ProductAttributes(productData: ProductDetailData) {
    if (!productData.place.isNullOrEmpty()) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(8.dp)
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                Text(
                    text = "商品属性",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = RoyalBlue
                )

                Spacer(modifier = Modifier.height(8.dp))

                productData.place.forEach { place ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 2.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "${place.prompt}:",
                            fontSize = 13.sp,
                            color = Color.Gray,
                            modifier = Modifier.weight(1f)
                        )
                        Text(
                            text = place.value ?: "",
                            fontSize = 13.sp,
                            color = Color.Black,
                            modifier = Modifier.weight(2f)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 商品评论信息组件
 */
@Composable
private fun ProductCommentInfo(productData: ProductDetailData) {
    productData.comment?.let { comment ->
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(8.dp)
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                Text(
                    text = "用户评价",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = RoyalBlue
                )

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 评分星星
                    repeat(5) { index ->
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = "星星",
                            tint = if (index < 5) Color(0xFFFFD700) else Color.Gray,
                            modifier = Modifier.size(16.dp)
                        )
                    }

                    Text(
                        text = "${comment.realPositiveRate ?: 0.0}%",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.Green
                    )
                }

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = "共 ${comment.count ?: 0} 条评价",
                    fontSize = 12.sp,
                    color = Color.Gray
                )

                // 评论标签
                comment.commentTagInfos?.take(3)?.let { tags ->
                    if (tags.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        LazyRow(
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            items(tags) { tag ->
                                Surface(
                                    color = Color(0xFFF5F5F5),
                                    shape = RoundedCornerShape(12.dp)
                                ) {
                                    Text(
                                        text = "${tag.name} (${tag.count})",
                                        fontSize = 11.sp,
                                        color = Color.Gray,
                                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 商品促销信息组件
 */
@Composable
private fun ProductPromotionInfo(productData: ProductDetailData) {
    productData.promotion?.let { promotion ->
        if (!promotion.promonames.isNullOrEmpty() || !promotion.coupons.isNullOrEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color.White),
                shape = RoundedCornerShape(8.dp)
            ) {
                Column(
                    modifier = Modifier.padding(12.dp)
                ) {
                    Text(
                        text = "促销信息",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = RoyalBlue
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // 促销活动
                    if (!promotion.promonames.isNullOrEmpty()) {
                        Surface(
                            color = Color(0xFFFFEBEE),
                            shape = RoundedCornerShape(6.dp)
                        ) {
                            Text(
                                text = promotion.promonames,
                                fontSize = 12.sp,
                                color = Color.Red,
                                modifier = Modifier.padding(8.dp)
                            )
                        }
                    }

                    // 优惠券
                    promotion.coupons?.take(2)?.let { coupons ->
                        if (coupons.isNotEmpty()) {
                            Spacer(modifier = Modifier.height(8.dp))
                            coupons.forEach { coupon ->
                                Surface(
                                    color = Color(0xFFFFF3E0),
                                    shape = RoundedCornerShape(6.dp),
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Text(
                                        text = coupon.showDescription ?: coupon.name ?: "",
                                        fontSize = 12.sp,
                                        color = Color(0xFFFF9800),
                                        modifier = Modifier.padding(8.dp)
                                    )
                                }
                                Spacer(modifier = Modifier.height(4.dp))
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 卖家信息组件
 */
@Composable
private fun ProductSellerInfo(productData: ProductDetailData) {
    productData.seller?.let { seller ->
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(8.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 卖家图标
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(seller.icon)
                        .crossfade(true)
                        .build(),
                    contentDescription = "卖家图标",
                    modifier = Modifier
                        .size(40.dp)
                        .clip(RoundedCornerShape(20.dp)),
                    contentScale = ContentScale.Crop
                )

                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "卖家信息",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    Text(
                        text = seller.title ?: "未知卖家",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.Black
                    )
                }
            }
        }
    }
}

/**
 * 底部操作栏组件
 */
@Composable
private fun ProductActionBar(
    productData: ProductDetailData,
    quantity: Int,
    onQuantityChange: (Int) -> Unit,
    onAddToCart: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 数量选择器
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "数量:",
                    fontSize = 14.sp,
                    color = Color.Gray
                )

                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    // 减少按钮
                    OutlinedButton(
                        onClick = {
                            if (quantity > 1) onQuantityChange(quantity - 1)
                        },
                        modifier = Modifier.size(32.dp),
                        contentPadding = PaddingValues(0.dp)
                    ) {
                        Text("-", fontSize = 16.sp)
                    }

                    // 数量显示
                    Text(
                        text = quantity.toString(),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(horizontal = 8.dp)
                    )

                    // 增加按钮
                    OutlinedButton(
                        onClick = {
                            val maxQuantity = productData.stock?.count ?: 999
                            if (quantity < maxQuantity) onQuantityChange(quantity + 1)
                        },
                        modifier = Modifier.size(32.dp),
                        contentPadding = PaddingValues(0.dp)
                    ) {
                        Text("+", fontSize = 16.sp)
                    }
                }
            }

            // 添加到购物车按钮
            Button(
                onClick = onAddToCart,
                enabled = (productData.price?.canBuy == 1) && (productData.stock?.count ?: 0) > 0,
                colors = ButtonDefaults.buttonColors(
                    containerColor = RoyalBlue,
                    disabledContainerColor = Color.Gray
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.ShoppingCart,
                    contentDescription = "购物车",
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = if ((productData.price?.canBuy == 1) && (productData.stock?.count ?: 0) > 0)
                        "加入购物车" else "暂时缺货",
                    fontSize = 14.sp
                )
            }
        }
    }
}
